$json = @"
{
  "title": "Hello Kotlin",
  "content": "Kotlin full stack",
  "color": 123
}
"@

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/notes" -Method POST -Body $json -ContentType "application/json" -UseBasicParsing
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
