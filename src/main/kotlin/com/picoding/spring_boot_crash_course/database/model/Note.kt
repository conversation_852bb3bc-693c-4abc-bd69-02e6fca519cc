import org.bson.types.ObjectId
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.util.Date

@Document("notes")
data class Note(
    val title: String,
    val content: String,
    val color: Long,
    val createdAt: Date = Date(), // <- use Date instead of Instant
    val ownerId: ObjectId,
    @Id val id: ObjectId = ObjectId.get()
)
